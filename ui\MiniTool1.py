import sys
import os
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget,
                             QVBoxLayout, QHBoxLayout, QPushButton,
                             QLineEdit, QTableWidget, QTableWidgetItem, QTextEdit,
                             QFileDialog, QLabel, QHeaderView, QMessageBox, QCheckBox, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

# Import MVC components
from models.file_model import FileModel, FileInfo
from models.command_model import CommandModel, CommandInfo
from models.configuration_model import ConfigurationModel
from controllers.main_controller import MainController


class MiniTool1(QMainWindow):
    """Main View class for MiniTool1 application - handles only UI components"""

    def __init__(self):
        super().__init__()

        # Initialize MVC components
        self.file_model = FileModel()
        self.command_model = CommandModel()
        self.config_model = ConfigurationModel()
        self.controller = MainController(self.file_model, self.command_model, self.config_model, self)

        # Store command row widgets for management
        self.command_row_widgets = []

        self.init_ui()

    def handle_model_change(self, event_type: str, data=None):
        """Handle model change notifications from controller"""
        if event_type == 'file_added':
            self.update_file_table()
        elif event_type == 'file_removed':
            self.update_file_table()
        elif event_type == 'command_added':
            # Command rows are managed dynamically, no need to update here
            pass
        elif event_type == 'command_removed':
            # Command rows are managed dynamically, no need to update here
            pass
        elif event_type == 'command_executed':
            # Update timestamp in the UI if needed
            pass

    def update_file_table(self):
        """Update the file table with current model data"""
        files = self.controller.get_files()
        self.file_table.setRowCount(len(files))

        for row, file_info in enumerate(files):
            self.file_table.setItem(row, 0, QTableWidgetItem(file_info.file_name))
            self.file_table.setItem(row, 1, QTableWidgetItem(file_info.start_address))
            self.file_table.setItem(row, 2, QTableWidgetItem(file_info.dlc))
            self.file_table.setItem(row, 3, QTableWidgetItem(file_info.crc))
            self.file_table.setItem(row, 4, QTableWidgetItem(file_info.note))

    def init_ui(self):
        self.setWindowTitle("MiniTool1")
        self.setGeometry(100, 100, 1000, 700)

        # Set gray background for the main window
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QWidget {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border: 1px solid #c0c0c0;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #f0f0f0;
                border-bottom: 1px solid #f0f0f0;
            }
        """)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.cutting_padding_tab = self.create_cutting_padding_tab()
        self.dll_checking_tab = self.create_dll_checking_tab()
        self.crc_checking_tab = self.create_crc_checking_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.cutting_padding_tab, "Cutting/Padding")
        self.tab_widget.addTab(self.dll_checking_tab, "DLL Checking")
        self.tab_widget.addTab(self.crc_checking_tab, "CRC Checking")

        # Set main layout
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tab_widget)
        central_widget.setLayout(main_layout)

    def create_cutting_padding_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Top section with file selection
        top_layout = QHBoxLayout()

        # Software file input
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("path/to/software file")
        self.file_path_input.setMinimumHeight(30)
        self.file_path_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 1px solid #c0c0c0;
                padding: 5px;
                border-radius: 3px;
            }
        """)

        self.software_file_btn = QPushButton("📁 Software file")
        self.software_file_btn.setMinimumHeight(30)
        self.software_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #d8d8d8;
            }
            QPushButton:pressed {
                background-color: #c8c8c8;
            }
        """)
        self.software_file_btn.clicked.connect(self.select_software_file)
        
        self.Convert_file = QPushButton("Convert")
        self.Convert_file.setMinimumHeight(30)
        self.Convert_file.setStyleSheet("""
            QPushButton {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #d8d8d8;
            }
            QPushButton:pressed {
                background-color: #c8c8c8;
            }
        """)
        self.Convert_file.clicked.connect(self.convert_action)
        
        # Warning icon (placeholder)
        # warning_label = QLabel("⚠️")
        # warning_label.setStyleSheet("color: red; font-size: 16px;")

        top_layout.addWidget(self.file_path_input, 3)
        top_layout.addWidget(self.software_file_btn, 1)
        top_layout.addWidget(self.Convert_file, 1)
        # top_layout.addWidget(warning_label)

        # Console section
        console_layout = QHBoxLayout()

        # Left side - table and buttons
        left_layout = QVBoxLayout()

        # Table
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(5)
        self.file_table.setHorizontalHeaderLabels(["File Name", "Start Address", "DLC", "CRC", "Note"])

        # Set table style
        self.file_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #c0c0c0;
            }
            QHeaderView::section {
                background-color: #e8e8e8;
                padding: 8px;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
        """)

        # Make table headers resizable
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)

        # Set minimum column widths
        self.file_table.setColumnWidth(0, 200)
        self.file_table.setColumnWidth(1, 120)
        self.file_table.setColumnWidth(2, 80)
        self.file_table.setColumnWidth(3, 80)

        left_layout.addWidget(self.file_table)

        # Middle buttons section
        middle_buttons_layout = QHBoxLayout()

        self.default_cut_btn = QPushButton("Default cut")
        self.default_cut_btn.setStyleSheet(self.get_button_style())
        self.default_cut_btn.clicked.connect(self.default_cut_action)

        self.padding_btn = QPushButton("Padding")
        self.padding_btn.setStyleSheet(self.get_button_style())
        self.padding_btn.clicked.connect(self.padding_action)

        self.load_config_btn = QPushButton("Load cut configuration")
        self.load_config_btn.setStyleSheet(self.get_button_style())
        self.load_config_btn.clicked.connect(self.load_cut_configuration)

        self.plus_btn = QPushButton("+")
        self.plus_btn.setMaximumWidth(40)
        self.plus_btn.setStyleSheet(self.get_button_style())
        self.plus_btn.clicked.connect(self.plus_action)

        middle_buttons_layout.addWidget(self.default_cut_btn)
        middle_buttons_layout.addWidget(self.padding_btn)
        middle_buttons_layout.addStretch()
        middle_buttons_layout.addWidget(self.load_config_btn)
        middle_buttons_layout.addWidget(self.plus_btn)

        left_layout.addLayout(middle_buttons_layout)

        # Multiple files chosen section (Command rows area)
        multiple_files_label = QLabel("Cut configuration area")
        multiple_files_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        left_layout.addWidget(multiple_files_label)

        # Scrollable area for command rows
        self.command_scroll_area = QScrollArea()
        self.command_scroll_area.setMaximumHeight(200)
        self.command_scroll_area.setWidgetResizable(True)
        self.command_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.command_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.command_scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: white;
                border: 1px solid #c0c0c0;
                border-radius: 3px;
            }
        """)

        # Widget to contain command rows
        self.command_rows_widget = QWidget()
        self.command_rows_layout = QVBoxLayout()
        self.command_rows_layout.setContentsMargins(5, 5, 5, 5)
        self.command_rows_widget.setLayout(self.command_rows_layout)

        # Add stretch to push command rows to top
        self.command_rows_layout.addStretch()

        self.command_scroll_area.setWidget(self.command_rows_widget)
        left_layout.addWidget(self.command_scroll_area)

        # Bottom buttons
        bottom_buttons_layout = QHBoxLayout()

        self.custom_cut_btn = QPushButton("Custom cut")
        self.custom_cut_btn.setStyleSheet(self.get_button_style())
        self.custom_cut_btn.clicked.connect(self.custom_cut_action)

        self.save_config_btn = QPushButton("Save Config")
        self.save_config_btn.setStyleSheet(self.get_button_style())
        self.save_config_btn.clicked.connect(self.save_config_action)

        bottom_buttons_layout.addWidget(self.custom_cut_btn)
        bottom_buttons_layout.addWidget(self.save_config_btn)
        bottom_buttons_layout.addStretch()
 
        left_layout.addLayout(bottom_buttons_layout)

        # Combine left and right layouts
        console_layout.addLayout(left_layout, 4)

        # Add all sections to main tab layout
        layout.addLayout(top_layout)
        layout.addLayout(console_layout)

        tab.setLayout(layout)
        return tab

    def get_button_style(self):
        """Return standard button style for gray theme"""
        return """
            QPushButton {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d8d8d8;
            }
            QPushButton:pressed {
                background-color: #c8c8c8;
            }
        """

    def create_dll_checking_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Placeholder content for DLL Checking tab
        label = QLabel("DLL Checking Tab")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setFont(QFont("Arial", 16))

        layout.addWidget(label)
        tab.setLayout(layout)
        return tab

    def create_crc_checking_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Placeholder content for CRC Checking tab
        label = QLabel("CRC Checking Tab")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setFont(QFont("Arial", 16))

        layout.addWidget(label)
        tab.setLayout(layout)
        return tab

    # Button action methods - now delegate to controller
    def select_software_file(self):
        """Handle software file selection through controller"""
        file_path = self.controller.select_software_file(self)
        if file_path:
            self.file_path_input.setText(file_path)

    def default_cut_action(self):
        """Handle default cut action through controller"""
        self.controller.default_cut_action(self)

    def padding_action(self):
        """Handle padding action through controller"""
        self.controller.padding_action(self)

    def load_cut_configuration(self):
        """Handle load cut configuration through controller"""
        self.controller.load_cut_configuration(self)

    def plus_action(self):
        """Handle adding new command row through controller"""
        command_index = self.controller.add_command_row()
        if command_index is not None:
            self.add_command_row()

    def custom_cut_action(self):
        """Handle custom cut action through controller"""
        self.controller.custom_cut_action(self)

    def save_config_action(self):
        """Handle save config action through controller"""
        self.controller.save_configuration(self)

    def convert_action(self):
        """Handle convert action through controller"""
        self.controller.convert_action(self)

    def add_command_row(self):
        """Add a new command row with input fields and buttons"""
        row_widget = QWidget()
        row_widget.setStyleSheet("background-color: #f8f8f8; border: 1px solid #ddd; margin: 2px; border-radius: 5px;")
        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(8, 8, 8, 8)

        # Start Address input
        start_address_input = QLineEdit()
        start_address_input.setPlaceholderText("Start Address")
        start_address_input.setFixedWidth(150)
        start_address_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # DLC value input
        dlc_input = QLineEdit()
        dlc_input.setPlaceholderText("DLC value")
        dlc_input.setFixedWidth(100)
        dlc_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # Check box
        checkbox = QCheckBox("Padding")
        checkbox.setFixedWidth(80)

        # Padding value input
        padding_input = QLineEdit()
        padding_input.setPlaceholderText("padding value")
        padding_input.setFixedWidth(100)
        padding_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # Cut button
        cut_btn = QPushButton("Cut")
        cut_btn.setFixedWidth(60)
        cut_btn.setFixedHeight(30)
        cut_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        # Delete button
        delete_btn = QPushButton("✕ Delete")
        delete_btn.setFixedWidth(70)
        delete_btn.setFixedHeight(30)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        # Timestamp label
        Info_filecut_label = QLabel("FileName + TimeStamp")
        Info_filecut_label.setFixedWidth(160)
        Info_filecut_label.setStyleSheet("""
            background-color: #e0e0e0;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
            text-align: center;
        """)
        Info_filecut_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Add widgets to layout
        row_layout.addWidget(start_address_input)
        row_layout.addWidget(dlc_input)
        row_layout.addWidget(checkbox)
        row_layout.addWidget(padding_input)
        row_layout.addWidget(cut_btn)
        row_layout.addWidget(delete_btn)
        row_layout.addWidget(Info_filecut_label)
        row_layout.addStretch()

        row_widget.setLayout(row_layout)

        # Connect button actions
        cut_btn.clicked.connect(lambda: self.cut_action(Info_filecut_label, start_address_input, dlc_input, checkbox, padding_input))
        delete_btn.clicked.connect(lambda: self.delete_command_row(row_widget))

        # Remove the stretch before adding new row, then add stretch back
        self.command_rows_layout.takeAt(self.command_rows_layout.count() - 1)
        self.command_rows_layout.addWidget(row_widget)
        self.command_rows_layout.addStretch()

        # Store reference to the row widget for deletion
        if not hasattr(self, 'command_row_widgets'):
            self.command_row_widgets = []
        self.command_row_widgets.append(row_widget)

        # Scroll to bottom to show new row
        self.command_scroll_area.verticalScrollBar().setValue(
            self.command_scroll_area.verticalScrollBar().maximum()
        )

    def cut_action(self, timestamp_label, start_address_input, dlc_input, checkbox, padding_input):
        """Handle cut button action and update timestamp"""
        current_time = datetime.now().strftime("%H:%M:%S")
        timestamp_label.setText(current_time)

        # Log the cut action with parameters
        start_addr = start_address_input.text() or "Not set"
        dlc_val = dlc_input.text() or "Not set"
        is_checked = "Yes" if checkbox.isChecked() else "No"
        padding_val = padding_input.text() or "Not set"

        log_message = f"Cut executed at {current_time} - Start: {start_addr}, DLC: {dlc_val}, Checked: {is_checked}, Padding: {padding_val}"
        # self.console_log.append(log_message)

    def delete_command_row(self, row_widget):
        """Delete a command row"""
        self.command_rows_layout.removeWidget(row_widget)
        row_widget.deleteLater()

        if hasattr(self, 'command_row_widgets'):
            if row_widget in self.command_row_widgets:
                self.command_row_widgets.remove(row_widget)

        # self.console_log.append("Command row deleted")

def main():
    app = QApplication(sys.argv)
    window = MiniTool1()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()