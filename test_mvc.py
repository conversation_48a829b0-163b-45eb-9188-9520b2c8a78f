#!/usr/bin/env python3
"""
Test script to verify MVC structure works correctly
"""
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_models():
    """Test model classes"""
    print("Testing Models...")
    
    # Test FileModel
    from models.file_model import FileModel, FileInfo
    file_model = FileModel()
    
    # Test adding a file
    test_file_path = __file__  # Use this script as test file
    success = file_model.add_file(test_file_path)
    print(f"Add file success: {success}")
    print(f"File count: {file_model.get_file_count()}")
    
    files = file_model.get_files()
    if files:
        print(f"First file: {files[0].file_name}")
    
    # Test CommandModel
    from models.command_model import CommandModel
    command_model = CommandModel()
    
    command_index = command_model.add_command("0x1000", "256", True, "0xFF")
    print(f"Added command at index: {command_index}")
    print(f"Command count: {command_model.get_command_count()}")
    
    # Test ConfigurationModel
    from models.configuration_model import ConfigurationModel
    config_model = ConfigurationModel()
    
    config_model.set_config_value("test_key", "test_value")
    value = config_model.get_config_value("test_key")
    print(f"Config value: {value}")
    
    print("Models test completed successfully!\n")

def test_controller():
    """Test controller class"""
    print("Testing Controller...")
    
    from models.file_model import FileModel
    from models.command_model import CommandModel
    from models.configuration_model import ConfigurationModel
    from controllers.main_controller import MainController
    
    # Create models
    file_model = FileModel()
    command_model = CommandModel()
    config_model = ConfigurationModel()
    
    # Create controller
    controller = MainController(file_model, command_model, config_model)
    
    # Test file operations
    files = controller.get_files()
    print(f"Initial file count: {len(files)}")
    
    # Test command operations
    commands = controller.get_commands()
    print(f"Initial command count: {len(commands)}")
    
    print("Controller test completed successfully!\n")

def test_imports():
    """Test that all imports work correctly"""
    print("Testing Imports...")
    
    try:
        from models.file_model import FileModel, FileInfo
        print("✓ FileModel imports work")
        
        from models.command_model import CommandModel, CommandInfo
        print("✓ CommandModel imports work")
        
        from models.configuration_model import ConfigurationModel
        print("✓ ConfigurationModel imports work")
        
        from controllers.main_controller import MainController
        print("✓ MainController imports work")
        
        print("All imports successful!\n")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== MVC Structure Test ===\n")
    
    # Test imports first
    if not test_imports():
        print("Import tests failed. Cannot continue.")
        return False
    
    # Test models
    try:
        test_models()
    except Exception as e:
        print(f"Model tests failed: {e}")
        return False
    
    # Test controller
    try:
        test_controller()
    except Exception as e:
        print(f"Controller tests failed: {e}")
        return False
    
    print("=== All Tests Passed! ===")
    print("MVC structure is working correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
