# PyQt CAN Tool

This is a PyQt desktop application template that supports:
- CAN log analysis
- Firmware file modification (SREC, HEX, BIN)
- DLL checking
- CRC calculation

## Folder Structure
pyqt_can_tool/
│── main.py                  # Entry point of your PyQt app
│── requirements.txt         # Dependencies (PyQt6, python-can, etc.)
│── README.md                # Project documentation
│
├── ui/                      # UI design files (.ui from Qt Designer)

├── views/                   # Python code for UI (converted from .ui or custom)
│
├── controllers/             # Connects views with services (business logic)
│
├── services/                # Core backend functionality
│   ├── can/
│   │
│   ├── firmware/
│   │
│   ├── dll/
│   │
│   └── crc/
│
├── models/                  # Data structures
│
├── tests/                   # Unit & integration tests
│
└── assets/                  # Static files (icons, sample logs, firmware)
    ├── icons/
    └── samples/
