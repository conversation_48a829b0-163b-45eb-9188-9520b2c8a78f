"""
File Model - Handles file data and operations
"""
import os
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class FileInfo:
    """Data class to represent file information"""
    file_path: str
    file_name: str
    start_address: str = "0x00000000"
    dlc: str = "0"
    crc: str = "0x0000"
    note: str = ""
    
    def __post_init__(self):
        if not self.file_name:
            self.file_name = os.path.basename(self.file_path)


class FileModel:
    """Model class to handle file data and operations"""
    
    def __init__(self):
        self._files: List[FileInfo] = []
        self._observers = []
    
    def add_observer(self, observer):
        """Add observer for model changes"""
        self._observers.append(observer)
    
    def remove_observer(self, observer):
        """Remove observer"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notify_observers(self, event_type: str, data=None):
        """Notify all observers of model changes"""
        for observer in self._observers:
            if hasattr(observer, 'model_changed'):
                observer.model_changed(event_type, data)
    
    def add_file(self, file_path: str) -> bool:
        """Add a file to the model"""
        if not file_path or not os.path.exists(file_path):
            return False
        
        # Check if file already exists
        for file_info in self._files:
            if file_info.file_path == file_path:
                return False
        
        file_info = FileInfo(file_path=file_path, file_name=os.path.basename(file_path))
        self._files.append(file_info)
        self.notify_observers('file_added', file_info)
        return True
    
    def remove_file(self, index: int) -> bool:
        """Remove a file by index"""
        if 0 <= index < len(self._files):
            removed_file = self._files.pop(index)
            self.notify_observers('file_removed', {'index': index, 'file': removed_file})
            return True
        return False
    
    def get_files(self) -> List[FileInfo]:
        """Get all files"""
        return self._files.copy()
    
    def get_file_count(self) -> int:
        """Get number of files"""
        return len(self._files)
    
    def update_file_info(self, index: int, **kwargs) -> bool:
        """Update file information"""
        if 0 <= index < len(self._files):
            file_info = self._files[index]
            for key, value in kwargs.items():
                if hasattr(file_info, key):
                    setattr(file_info, key, value)
            self.notify_observers('file_updated', {'index': index, 'file': file_info})
            return True
        return False
    
    def clear_files(self):
        """Clear all files"""
        self._files.clear()
        self.notify_observers('files_cleared', None)
    
    def get_file_by_index(self, index: int) -> Optional[FileInfo]:
        """Get file by index"""
        if 0 <= index < len(self._files):
            return self._files[index]
        return None
