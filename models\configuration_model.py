"""
Configuration Model - Handles configuration data and operations
"""
import json
import os
from typing import Dict, Any, Optional


class ConfigurationModel:
    """Model class to handle configuration data and operations"""
    
    def __init__(self):
        self._config_data: Dict[str, Any] = {}
        self._observers = []
        self._config_file_path: Optional[str] = None
    
    def add_observer(self, observer):
        """Add observer for model changes"""
        self._observers.append(observer)
    
    def remove_observer(self, observer):
        """Remove observer"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notify_observers(self, event_type: str, data=None):
        """Notify all observers of model changes"""
        for observer in self._observers:
            if hasattr(observer, 'model_changed'):
                observer.model_changed(event_type, data)
    
    def set_config_value(self, key: str, value: Any):
        """Set a configuration value"""
        self._config_data[key] = value
        self.notify_observers('config_updated', {'key': key, 'value': value})
    
    def get_config_value(self, key: str, default=None) -> Any:
        """Get a configuration value"""
        return self._config_data.get(key, default)
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration data"""
        return self._config_data.copy()
    
    def load_configuration(self, file_path: str) -> bool:
        """Load configuration from file"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
                self._config_file_path = file_path
                self.notify_observers('config_loaded', {'file_path': file_path})
                return True
        except Exception as e:
            self.notify_observers('config_load_error', {'error': str(e)})
        return False
    
    def save_configuration(self, file_path: Optional[str] = None) -> bool:
        """Save configuration to file"""
        save_path = file_path or self._config_file_path
        if not save_path:
            return False
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=2)
            self._config_file_path = save_path
            self.notify_observers('config_saved', {'file_path': save_path})
            return True
        except Exception as e:
            self.notify_observers('config_save_error', {'error': str(e)})
        return False
    
    def clear_configuration(self):
        """Clear all configuration data"""
        self._config_data.clear()
        self._config_file_path = None
        self.notify_observers('config_cleared', None)
    
    def update_configuration(self, config_dict: Dict[str, Any]):
        """Update configuration with a dictionary"""
        self._config_data.update(config_dict)
        self.notify_observers('config_bulk_updated', config_dict)
