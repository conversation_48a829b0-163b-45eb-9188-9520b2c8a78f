"""
Main Controller - Handles business logic and coordinates between models and views
"""
import os
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject

from models.file_model import FileModel
from models.command_model import CommandModel
from models.configuration_model import ConfigurationModel


class MainController(QObject):
    """Main controller class that handles business logic and coordinates MVC components"""
    
    def __init__(self, file_model: FileModel, command_model: CommandModel, 
                 config_model: ConfigurationModel, view=None):
        super().__init__()
        self.file_model = file_model
        self.command_model = command_model
        self.config_model = config_model
        self.view = view
        
        # Set up model observers
        self.file_model.add_observer(self)
        self.command_model.add_observer(self)
        self.config_model.add_observer(self)
    
    def set_view(self, view):
        """Set the view reference"""
        self.view = view
    
    def model_changed(self, event_type: str, data=None):
        """Handle model change notifications"""
        if self.view:
            self.view.handle_model_change(event_type, data)
    
    # File operations
    def select_software_file(self, parent_widget=None):
        """Handle software file selection"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            parent_widget,
            "Select Software File",
            "",
            "Firmware Files (*.hex *.bin *.srec);;All Files (*)"
        )
        
        if file_path:
            success = self.file_model.add_file(file_path)
            if success:
                return file_path
            else:
                if parent_widget:
                    QMessageBox.warning(parent_widget, "Warning", 
                                      "File already exists or is invalid")
        return None
    
    def remove_file(self, index: int):
        """Remove a file by index"""
        return self.file_model.remove_file(index)
    
    def get_files(self):
        """Get all files"""
        return self.file_model.get_files()
    
    def update_file_info(self, index: int, **kwargs):
        """Update file information"""
        return self.file_model.update_file_info(index, **kwargs)
    
    # Command operations
    def add_command_row(self):
        """Add a new command row"""
        if self.file_model.get_file_count() == 0:
            if self.view:
                QMessageBox.warning(self.view, "Warning", "No files chosen yet")
            return None
        
        return self.command_model.add_command()
    
    def remove_command(self, index: int):
        """Remove a command by index"""
        return self.command_model.remove_command(index)
    
    def execute_cut_command(self, index: int, start_address: str, dlc_value: str, 
                           is_checked: bool, padding_value: str):
        """Execute a cut command"""
        # Update command with current values
        self.command_model.update_command(
            index,
            start_address=start_address,
            dlc_value=dlc_value,
            is_checked=is_checked,
            padding_value=padding_value
        )
        
        # Execute the command
        success = self.command_model.execute_command(index)
        
        if success:
            # Here you would implement the actual cutting logic
            # For now, just show a placeholder message
            if self.view:
                QMessageBox.information(self.view, "Info", 
                                      f"Cut command executed with parameters:\n"
                                      f"Start Address: {start_address}\n"
                                      f"DLC: {dlc_value}\n"
                                      f"Checked: {is_checked}\n"
                                      f"Padding: {padding_value}")
        
        return success
    
    def get_commands(self):
        """Get all commands"""
        return self.command_model.get_commands()
    
    # Configuration operations
    def load_cut_configuration(self, parent_widget=None):
        """Load cut configuration from file"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            parent_widget,
            "Load Cut Configuration",
            "",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            success = self.config_model.load_configuration(file_path)
            if not success and parent_widget:
                QMessageBox.warning(parent_widget, "Error", 
                                  "Failed to load configuration file")
            return success
        return False
    
    def save_configuration(self, parent_widget=None):
        """Save current configuration to file"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            parent_widget,
            "Save Configuration",
            "",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            # Collect current state data
            config_data = {
                'files': [
                    {
                        'file_path': f.file_path,
                        'start_address': f.start_address,
                        'dlc': f.dlc,
                        'crc': f.crc,
                        'note': f.note
                    }
                    for f in self.file_model.get_files()
                ],
                'commands': [
                    {
                        'start_address': c.start_address,
                        'dlc_value': c.dlc_value,
                        'is_checked': c.is_checked,
                        'padding_value': c.padding_value
                    }
                    for c in self.command_model.get_commands()
                ]
            }
            
            self.config_model.update_configuration(config_data)
            success = self.config_model.save_configuration(file_path)
            
            if not success and parent_widget:
                QMessageBox.warning(parent_widget, "Error",
                                  "Failed to save configuration file")
            return success
        return False

    # Business logic operations (placeholder methods for future implementation)
    def default_cut_action(self, parent_widget=None):
        """Handle default cut action"""
        if parent_widget:
            QMessageBox.information(parent_widget, "Info", "Default cut action - Not implemented yet")

    def padding_action(self, parent_widget=None):
        """Handle padding action"""
        if parent_widget:
            QMessageBox.information(parent_widget, "Info", "Padding action - Not implemented yet")

    def custom_cut_action(self, parent_widget=None):
        """Handle custom cut action"""
        if parent_widget:
            QMessageBox.information(parent_widget, "Info", "Custom cut action - Not implemented yet")

    def convert_action(self, parent_widget=None):
        """Handle convert action"""
        if parent_widget:
            QMessageBox.information(parent_widget, "Info", "Convert action - Not implemented yet")
