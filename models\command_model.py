"""
Command Model - Handles command data and operations
"""
from typing import List, Dict, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CommandInfo:
    """Data class to represent command information"""
    start_address: str = ""
    dlc_value: str = ""
    is_checked: bool = False
    padding_value: str = ""
    timestamp: str = ""
    status: str = "pending"  # pending, executed, failed
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().strftime("%H:%M:%S")


class CommandModel:
    """Model class to handle command data and operations"""
    
    def __init__(self):
        self._commands: List[CommandInfo] = []
        self._observers = []
    
    def add_observer(self, observer):
        """Add observer for model changes"""
        self._observers.append(observer)
    
    def remove_observer(self, observer):
        """Remove observer"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def notify_observers(self, event_type: str, data=None):
        """Notify all observers of model changes"""
        for observer in self._observers:
            if hasattr(observer, 'model_changed'):
                observer.model_changed(event_type, data)
    
    def add_command(self, start_address: str = "", dlc_value: str = "", 
                   is_checked: bool = False, padding_value: str = "") -> int:
        """Add a new command and return its index"""
        command = CommandInfo(
            start_address=start_address,
            dlc_value=dlc_value,
            is_checked=is_checked,
            padding_value=padding_value
        )
        self._commands.append(command)
        index = len(self._commands) - 1
        self.notify_observers('command_added', {'index': index, 'command': command})
        return index
    
    def remove_command(self, index: int) -> bool:
        """Remove a command by index"""
        if 0 <= index < len(self._commands):
            removed_command = self._commands.pop(index)
            self.notify_observers('command_removed', {'index': index, 'command': removed_command})
            return True
        return False
    
    def get_commands(self) -> List[CommandInfo]:
        """Get all commands"""
        return self._commands.copy()
    
    def get_command_count(self) -> int:
        """Get number of commands"""
        return len(self._commands)
    
    def update_command(self, index: int, **kwargs) -> bool:
        """Update command information"""
        if 0 <= index < len(self._commands):
            command = self._commands[index]
            for key, value in kwargs.items():
                if hasattr(command, key):
                    setattr(command, key, value)
            self.notify_observers('command_updated', {'index': index, 'command': command})
            return True
        return False
    
    def execute_command(self, index: int) -> bool:
        """Mark command as executed and update timestamp"""
        if 0 <= index < len(self._commands):
            command = self._commands[index]
            command.status = "executed"
            command.timestamp = datetime.now().strftime("%H:%M:%S")
            self.notify_observers('command_executed', {'index': index, 'command': command})
            return True
        return False
    
    def clear_commands(self):
        """Clear all commands"""
        self._commands.clear()
        self.notify_observers('commands_cleared', None)
    
    def get_command_by_index(self, index: int) -> Optional[CommandInfo]:
        """Get command by index"""
        if 0 <= index < len(self._commands):
            return self._commands[index]
        return None
