#!/usr/bin/env python3
"""
Comprehensive verification script for MVC integration
"""
import sys
import os
import tempfile
import json

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_model_integration():
    """Test file model with realistic scenarios"""
    print("Testing File Model Integration...")
    
    from models.file_model import FileModel
    
    model = FileModel()
    
    # Test observer pattern
    class TestObserver:
        def __init__(self):
            self.events = []
        
        def model_changed(self, event_type, data):
            self.events.append((event_type, data))
    
    observer = TestObserver()
    model.add_observer(observer)
    
    # Test adding files
    test_file = __file__
    success = model.add_file(test_file)
    assert success, "Should be able to add file"
    assert len(observer.events) == 1, "Should notify observer"
    assert observer.events[0][0] == 'file_added', "Should send correct event"
    
    # Test duplicate file
    success = model.add_file(test_file)
    assert not success, "Should not add duplicate file"
    
    # Test file info update
    success = model.update_file_info(0, start_address="0x8000", dlc="512")
    assert success, "Should update file info"
    
    files = model.get_files()
    assert len(files) == 1, "Should have one file"
    assert files[0].start_address == "0x8000", "Should update start address"
    assert files[0].dlc == "512", "Should update DLC"
    
    print("✓ File Model Integration test passed")

def test_command_model_integration():
    """Test command model with realistic scenarios"""
    print("Testing Command Model Integration...")
    
    from models.command_model import CommandModel
    
    model = CommandModel()
    
    # Test adding commands
    index = model.add_command("0x1000", "256", True, "0xFF")
    assert index == 0, "First command should have index 0"
    
    # Test command execution
    success = model.execute_command(0)
    assert success, "Should execute command"
    
    commands = model.get_commands()
    assert len(commands) == 1, "Should have one command"
    assert commands[0].status == "executed", "Command should be executed"
    assert commands[0].timestamp, "Should have timestamp"
    
    print("✓ Command Model Integration test passed")

def test_configuration_model_integration():
    """Test configuration model with file operations"""
    print("Testing Configuration Model Integration...")
    
    from models.configuration_model import ConfigurationModel
    
    model = ConfigurationModel()
    
    # Test configuration data
    test_config = {
        "files": [
            {"file_path": "/test/file.hex", "start_address": "0x8000"}
        ],
        "commands": [
            {"start_address": "0x1000", "dlc_value": "256"}
        ]
    }
    
    model.update_configuration(test_config)
    
    # Test saving to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_file = f.name
    
    try:
        success = model.save_configuration(temp_file)
        assert success, "Should save configuration"
        
        # Test loading
        model2 = ConfigurationModel()
        success = model2.load_configuration(temp_file)
        assert success, "Should load configuration"
        
        loaded_config = model2.get_all_config()
        assert loaded_config == test_config, "Loaded config should match saved config"
        
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    print("✓ Configuration Model Integration test passed")

def test_controller_integration():
    """Test controller with all models"""
    print("Testing Controller Integration...")
    
    from models.file_model import FileModel
    from models.command_model import CommandModel
    from models.configuration_model import ConfigurationModel
    from controllers.main_controller import MainController
    
    # Create models
    file_model = FileModel()
    command_model = CommandModel()
    config_model = ConfigurationModel()
    
    # Create controller
    controller = MainController(file_model, command_model, config_model)
    
    # Test file operations through controller
    files = controller.get_files()
    assert len(files) == 0, "Should start with no files"
    
    # Test command operations through controller
    commands = controller.get_commands()
    assert len(commands) == 0, "Should start with no commands"
    
    # Test adding command (should fail without files)
    command_index = controller.add_command_row()
    assert command_index is None, "Should not add command without files"
    
    # Add a file first
    file_model.add_file(__file__)
    
    # Now adding command should work
    command_index = controller.add_command_row()
    assert command_index == 0, "Should add command with files present"
    
    # Test command execution
    success = controller.execute_cut_command(0, "0x2000", "128", False, "0x00")
    assert success, "Should execute cut command"
    
    # Verify command was updated
    commands = controller.get_commands()
    assert len(commands) == 1, "Should have one command"
    assert commands[0].start_address == "0x2000", "Should update start address"
    assert commands[0].status == "executed", "Should be executed"
    
    print("✓ Controller Integration test passed")

def test_mvc_communication():
    """Test communication between MVC components"""
    print("Testing MVC Communication...")
    
    from models.file_model import FileModel
    from models.command_model import CommandModel
    from models.configuration_model import ConfigurationModel
    from controllers.main_controller import MainController
    
    # Create models
    file_model = FileModel()
    command_model = CommandModel()
    config_model = ConfigurationModel()
    
    # Mock view to test observer pattern
    class MockView:
        def __init__(self):
            self.model_changes = []
        
        def handle_model_change(self, event_type, data):
            self.model_changes.append((event_type, data))
    
    mock_view = MockView()
    
    # Create controller with mock view
    controller = MainController(file_model, command_model, config_model, mock_view)
    
    # Test that model changes reach the view
    file_model.add_file(__file__)
    
    # Check that view received notification
    assert len(mock_view.model_changes) > 0, "View should receive model change notifications"
    
    # Test command model communication
    command_model.add_command("0x3000", "64", True, "0xAA")
    
    # Verify view received command notification
    command_events = [event for event in mock_view.model_changes if event[0].startswith('command')]
    assert len(command_events) > 0, "View should receive command notifications"
    
    print("✓ MVC Communication test passed")

def main():
    """Run all integration tests"""
    print("=== MVC Integration Verification ===\n")
    
    tests = [
        test_file_model_integration,
        test_command_model_integration,
        test_configuration_model_integration,
        test_controller_integration,
        test_mvc_communication
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {e}")
            failed += 1
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All integration tests passed!")
        print("Your MVC refactoring is working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
